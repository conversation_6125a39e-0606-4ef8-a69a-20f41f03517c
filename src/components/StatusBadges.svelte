<script lang="ts">
  export let status: string | undefined = 'draft';
  export let visibility: string | undefined = 'public';
  export let featured: boolean | undefined = false;

  function getStatusColor(status: string): string {
    switch (status) {
      case 'published':
        return 'var(--color-green)';
      case 'scheduled':
        return 'var(--color-orange)';
      case 'draft':
      default:
        return 'var(--text-muted)';
    }
  }

  function getStatusIcon(status: string): string {
    switch (status) {
      case 'published':
        return 'lucide-check-circle';
      case 'scheduled':
        return 'lucide-clock';
      case 'draft':
      default:
        return 'lucide-edit';
    }
  }

  function getVisibilityColor(visibility: string): string {
    switch (visibility) {
      case 'public':
        return 'var(--color-blue)';
      case 'members':
        return 'var(--color-purple)';
      case 'paid':
        return 'var(--color-orange)';
      case 'tiers':
        return 'var(--color-red)';
      default:
        return 'var(--text-muted)';
    }
  }

  function getVisibilityIcon(visibility: string): string {
    switch (visibility) {
      case 'public':
        return 'lucide-globe';
      case 'members':
        return 'lucide-users';
      case 'paid':
        return 'lucide-credit-card';
      case 'tiers':
        return 'lucide-layers';
      default:
        return 'lucide-lock';
    }
  }
</script>

<div class="ghost-status-badges">
  <!-- Status Badge -->
  <div class="ghost-status-badge" style="--badge-color: {getStatusColor(status || 'draft')}">
    <span class="ghost-status-badge-icon {getStatusIcon(status || 'draft')}"></span>
    <span class="ghost-status-badge-value">{status || 'draft'}</span>
  </div>

  <!-- Visibility Badge -->
  <div class="ghost-status-badge" style="--badge-color: {getVisibilityColor(visibility || 'public')}">
    <span class="ghost-status-badge-icon {getVisibilityIcon(visibility || 'public')}"></span>
    <span class="ghost-status-badge-value">{visibility || 'public'}</span>
  </div>

  <!-- Featured Badge -->
  <div class="ghost-status-badge ghost-featured-badge" class:featured={featured}>
    <span class="ghost-status-badge-icon lucide-star"></span>
  </div>
</div>

<style>
  .ghost-status-badges {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }

  .ghost-status-badge {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    background: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    font-size: 11px;
    font-weight: 500;
    color: var(--text-muted);
    transition: all 0.2s ease;
    /* Ensure consistent height for all badges */
    min-height: 24px;
    box-sizing: border-box;
  }

  .ghost-status-badge-icon {
    opacity: 0.8;
    /* Ensure consistent icon sizing */
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .ghost-status-badge-value {
    color: var(--badge-color, var(--text-muted));
    font-weight: 600;
    text-transform: capitalize;
    /* Ensure consistent value sizing */
    line-height: 1;
  }

  .ghost-featured-badge {
    opacity: 0.5;
    /* Ensure featured badge has same dimensions as others */
    min-height: 24px;
  }

  .ghost-featured-badge.featured {
    opacity: 1;
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border-color: var(--interactive-accent);
    /* Maintain consistent sizing when active */
    min-height: 24px;
  }

  .ghost-featured-badge.featured .ghost-status-badge-icon {
    opacity: 1;
    color: var(--text-on-accent);
  }

  .ghost-featured-badge.featured .ghost-status-badge-label {
    color: var(--text-on-accent);
    opacity: 0.9;
  }
</style>
